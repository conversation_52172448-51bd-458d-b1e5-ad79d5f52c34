import axios from 'axios';
import {
  BadRequestException,
  UnauthorizedException,
  ForbiddenException,
  NotFoundException,
  ConflictException,
  InternalServerErrorException,
  ServiceUnavailableException,
  RequestTimeoutException,
} from '@nestjs/common';

export const handleErrors = (error: any) => {
  if (axios.isAxiosError(error)) {
    if (error.response) {
      const { status, data } = error.response;

      // Extract error message from various possible response formats
      let errorMessage = 'An error occurred';
      let errorDetails = null;

      if (typeof data === 'string') {
        errorMessage = data;
      } else if (data && typeof data === 'object') {
        // Handle different error response formats
        errorMessage = data.message || data.detail || data.error || data.error_description || 'An error occurred';
        errorDetails = data;
      }

      // Map HTTP status codes to appropriate NestJS exceptions
      switch (status) {
        case 400:
          throw new BadRequestException(errorMessage, { cause: errorDetails });
        case 401:
          throw new UnauthorizedException(errorMessage, { cause: errorDetails });
        case 403:
          throw new ForbiddenException(errorMessage, { cause: errorDetails });
        case 404:
          throw new NotFoundException(errorMessage, { cause: errorDetails });
        case 408:
          throw new RequestTimeoutException(errorMessage, { cause: errorDetails });
        case 409:
          throw new ConflictException(errorMessage, { cause: errorDetails });
        case 500:
          throw new InternalServerErrorException(errorMessage, { cause: errorDetails });
        case 503:
          throw new ServiceUnavailableException(errorMessage, { cause: errorDetails });
        default:
          // For other status codes, use BadRequestException as default
          throw new BadRequestException(errorMessage, { cause: errorDetails });
      }
    } else if (error.request) {
      // Network error or no response received
      throw new ServiceUnavailableException('Network error: Unable to reach the service');
    } else {
      // Something else happened
      throw new InternalServerErrorException(error.message || 'Request configuration error');
    }
  } else {
    // Not an Axios error, throw as internal server error
    throw new InternalServerErrorException(error.message || 'An unexpected error occurred');
  }
};
