import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { PaymentStrategy } from './strategies/payment.strategy';
import { PaymentGateway as PaymentGatewayEnum, TransactionStatus, TransactionType, SubscriptionStatus, Currency } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import {
  EsewaPaymentDto,
  IEsewaVerifyPayload,
  InitiatePaymentDto,
  KhaltiPaymentDto,
  StripePaymentDto,
  VerifyPaymentDto,
} from './dto/payment.dto';
import { Request } from 'express';
import { AuditService } from './services/audit.service';
import { AuditAction, AuditContext } from './dto/audit-log.dto';
import {
  EnhancedPaymentInitiationResponseDto,
  EnhancedPaymentVerificationResponseDto,
  PaymentHistoryQueryDto,
  SubscriptionTrackingResponseDto,
} from './dto/enhanced-payment.dto';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly paymentStrategy: PaymentStrategy,
    private readonly auditService: AuditService,
  ) {}

  /**
   * Initiate a payment transaction with database logging and audit trail
   */
  async initiatePayment(payload: InitiatePaymentDto, context?: AuditContext) {
    const startTime = Date.now();
    let transaction: any = null;
    let paymentMethod: any = null;
    let auditLogId: string | null = null;

    try {
      this.logger.log(`Initiating payment for gateway: ${payload.paymentGateway}`);

      // Get or create payment method
      paymentMethod = await this.getOrCreatePaymentMethod(payload.tenantId, payload.paymentGateway, payload.isPrimary);

      // Create initial transaction record
      transaction = await this.createTransaction({
        paymentMethodId: paymentMethod.id,
        planId: payload.planId,
        amount: 0, // Will be updated after getting plan details
        currency: payload.currency,
        status: TransactionStatus.PENDING,
        transactionType: TransactionType.CHARGE,
        purchaseOrderId: `order-${payload.tenantId}-${Date.now()}`,
        purchaseOrderName: `Payment for plan ${payload.planId}`,
        transactionDetails: {
          paymentGateway: payload.paymentGateway,
          initiatedAt: new Date().toISOString(),
          tenantId: payload.tenantId,
        },
      });

      // Get plan details and update transaction amount
      const gateway = this.paymentStrategy.getStrategy(payload.paymentGateway);
      const gatewayResponse = await gateway.initiatePayment(payload);

      // Update transaction with actual amount from plan
      const plan = await this.prismaService.plan.findUnique({
        where: { id: payload.planId },
      });

      if (plan) {
        await this.prismaService.transaction.update({
          where: { id: transaction.id },
          data: { amount: new Decimal(plan.amount) },
        });
        transaction.amount = plan.amount;
      }

      // Create audit log
      if (context) {
        const auditLog = await this.auditService.logPaymentInitiation(context, {
          transactionId: transaction.id,
          paymentGateway: payload.paymentGateway,
          amount: plan?.amount || 0,
          currency: payload.currency,
          planId: payload.planId,
          tenantId: payload.tenantId,
        });
        auditLogId = auditLog.id;
      }

      this.logger.log(`Payment initiated successfully`, {
        transactionId: transaction.id,
        paymentMethodId: paymentMethod.id,
        gateway: payload.paymentGateway,
        processingTime: Date.now() - startTime,
      });

      return {
        success: true,
        data: {
          gatewayResponse,
          transaction: {
            id: transaction.id,
            amount: transaction.amount,
            currency: transaction.currency,
            status: transaction.status,
            transactionType: transaction.transactionType,
            purchaseOrderId: transaction.purchaseOrderId,
            purchaseOrderName: transaction.purchaseOrderName,
            createdAt: transaction.createdAt,
          },
          paymentMethod: {
            id: paymentMethod.id,
            paymentGateway: paymentMethod.paymentGateway,
            isPrimary: paymentMethod.isPrimary,
            tenantId: paymentMethod.tenantId,
            createdAt: paymentMethod.createdAt,
          },
          redirectUrl: gatewayResponse.payment_url || gatewayResponse.url || gatewayResponse.redirectUrl,
          sessionId: gatewayResponse.session?.id || gatewayResponse.sessionId,
        },
        message: 'Payment initiated successfully',
        auditLogId,
      };
    } catch (error) {
      this.logger.error('Error initiating payment', {
        error: error.message,
        payload,
        transactionId: transaction?.id,
        processingTime: Date.now() - startTime,
      });

      // Log failed audit entry
      if (context) {
        await this.auditService
          .logPaymentInitiation(context, {
            transactionId: transaction?.id || 'unknown',
            paymentGateway: payload.paymentGateway,
            amount: 0,
            currency: payload.currency,
            planId: payload.planId,
            tenantId: payload.tenantId,
          })
          .catch(() => {}); // Don't fail if audit logging fails
      }

      throw new BadRequestException(error.message);
    }
  }

  /**
   * Verify a payment transaction with audit logging
   */
  async verifyPayment(payload: VerifyPaymentDto, context?: AuditContext) {
    try {
      const paymentGateway = payload.paymentGateway.toUpperCase() as PaymentGatewayEnum;
      const gateway = this.paymentStrategy.getStrategy(paymentGateway);

      let verificationResult: any;

      // Handle different payment gateway DTOs using strategy pattern
      switch (paymentGateway) {
        case PaymentGatewayEnum.KHALTI:
          verificationResult = await this.verifyKhaltiPayment(gateway, payload as KhaltiPaymentDto);
          break;
        case PaymentGatewayEnum.ESEWA:
          verificationResult = await this.verifyEsewaPayment(gateway, payload as EsewaPaymentDto);
          break;
        case PaymentGatewayEnum.STRIPE:
          verificationResult = await this.verifyStripePayment(gateway, payload as StripePaymentDto);
          break;
        default:
          throw new Error(`Unsupported payment gateway: ${paymentGateway}`);
      }

      return {
        success: true,
        data: verificationResult,
        message: 'Payment verification completed',
      };
    } catch (error) {
      this.logger.error('Error verifying payment', error);
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleStripeWebhook(signature: string, request: Request) {
    try {
      if (!signature) {
        this.logger.warn('Missing Stripe signature');
        return {
          success: false,
          error: 'Missing stripe-signature header',
          message: 'Webhook signature verification failed',
        };
      }

      // Get raw body from request
      const payload = request.body;

      // Convert body to buffer if it's not already
      const rawBody = Buffer.isBuffer(payload) ? payload : Buffer.from(JSON.stringify(payload));

      // Get Stripe gateway
      const stripeGateway = this.paymentStrategy.getStrategy(PaymentGatewayEnum.STRIPE);

      // Process webhook - ensure handleWebhook exists
      if (typeof stripeGateway.handleWebhook === 'function') {
        const result = await stripeGateway.handleWebhook(signature, rawBody);
        return {
          success: true,
          data: result,
          message: 'Webhook processed successfully',
        };
      } else {
        throw new Error('Webhook handler not implemented for this gateway');
      }
    } catch (error) {
      this.logger.error('Error processing Stripe webhook', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to process webhook',
      };
    }
  }

  /**
   * Private method to verify Khalti payment
   */
  private async verifyKhaltiPayment(gateway: any, payload: KhaltiPaymentDto) {
    // Check if pidx is provided for Khalti payments
    if (!payload.pidx) {
      throw new BadRequestException('Must provide pidx for Khalti payment verification');
    }

    return await gateway.verifyPayment({
      pidx: payload.pidx,
    });
  }

  /**
   * Private method to verify Esewa payment
   */
  private async verifyEsewaPayment(gateway: any, payload: EsewaPaymentDto) {
    // Check if data is provided for esewa payments
    if (!payload.data) {
      throw new BadRequestException('Must provide data for esewa payment verification');
    }

    const decoded = Buffer.from(payload.data, 'base64').toString();
    const decodedJson = JSON.parse(decoded);

    // The data property contains base64 encoded verification data
    return await gateway.verifyPayment(decodedJson as IEsewaVerifyPayload);
  }

  /**
   * Private method to verify Stripe payment
   */
  private async verifyStripePayment(gateway: any, payload: StripePaymentDto) {
    // Check if session_id is provided for stripe payments
    if (!payload.session_id) {
      throw new BadRequestException('Must provide session_id for stripe payment verification');
    }

    return await gateway.verifyPayment({
      session_id: payload.session_id,
    });
  }

  /**
   * Get or create payment method for a tenant
   */
  private async getOrCreatePaymentMethod(tenantId: string, paymentGateway: PaymentGatewayEnum, isPrimary?: boolean) {
    // Check if payment method already exists
    let paymentMethod = await this.prismaService.paymentMethod.findFirst({
      where: {
        tenantId,
        paymentGateway,
      },
    });

    if (!paymentMethod) {
      // Create new payment method
      paymentMethod = await this.prismaService.paymentMethod.create({
        data: {
          tenantId,
          paymentGateway,
          isPrimary: isPrimary || false,
        },
      });

      this.logger.log(`Created new payment method`, {
        paymentMethodId: paymentMethod.id,
        tenantId,
        paymentGateway,
      });
    } else if (isPrimary && !paymentMethod.isPrimary) {
      // Update to primary if requested
      await this.prismaService.paymentMethod.updateMany({
        where: { tenantId },
        data: { isPrimary: false },
      });

      paymentMethod = await this.prismaService.paymentMethod.update({
        where: { id: paymentMethod.id },
        data: { isPrimary: true },
      });
    }

    return paymentMethod;
  }

  /**
   * Create a new transaction record
   */
  private async createTransaction(data: {
    paymentMethodId?: string;
    subscriptionId?: string;
    planId: string;
    amount: number;
    currency: Currency;
    status: TransactionStatus;
    transactionType: TransactionType;
    purchaseOrderId?: string;
    purchaseOrderName?: string;
    transactionDetails: any;
  }) {
    return await this.prismaService.transaction.create({
      data: {
        paymentMethodId: data.paymentMethodId,
        subscriptionId: data.subscriptionId,
        planId: data.planId,
        amount: new Decimal(data.amount),
        currency: data.currency,
        status: data.status,
        transactionType: data.transactionType,
        purchaseOrderId: data.purchaseOrderId,
        purchaseOrderName: data.purchaseOrderName,
        transactionDetails: data.transactionDetails,
      },
    });
  }

  /**
   * Get payment history for a tenant
   */
  async getPaymentHistory(query: PaymentHistoryQueryDto) {
    const { tenantId, paymentGateway, status, transactionType, startDate, endDate, page, limit, sortBy, sortOrder } = query;

    const where: any = {};

    if (tenantId) {
      where.paymentMethod = {
        tenantId,
      };
    }
    if (paymentGateway) {
      where.paymentMethod = {
        ...where.paymentMethod,
        paymentGateway,
      };
    }
    if (status) where.status = status;
    if (transactionType) where.transactionType = transactionType;

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    const [transactions, total] = await Promise.all([
      this.prismaService.transaction.findMany({
        where,
        include: {
          paymentMethod: {
            select: {
              id: true,
              paymentGateway: true,
              isPrimary: true,
            },
          },
          plan: {
            select: {
              id: true,
              name: true,
              description: true,
              amount: true,
            },
          },
          subscription: {
            select: {
              id: true,
              status: true,
              startDate: true,
              endDate: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.prismaService.transaction.count({ where }),
    ]);

    return {
      success: true,
      data: {
        transactions: transactions.map(t => ({
          ...t,
          amount: Number(t.amount),
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
          hasNext: page * limit < total,
          hasPrev: page > 1,
        },
      },
      message: 'Payment history retrieved successfully',
    };
  }
}
