import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { PaymentStrategy } from './strategies/payment.strategy';
import { PaymentGateway as PaymentGatewayEnum } from '@prisma/client';
import {
  EsewaPaymentDto,
  IEsewaVerifyPayload,
  InitiatePaymentDto,
  KhaltiPaymentDto,
  StripePaymentDto,
  VerifyPaymentDto,
} from './dto/payment.dto';
import { Request } from 'express';

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly paymentStrategy: PaymentStrategy,
  ) {}

  /**
   * Initiate a payment transaction
   */
  async initiatePayment(payload: InitiatePaymentDto) {
    try {
      this.logger.log(`Initiating payment for gateway: ${payload.paymentGateway}`);
      const gateway = this.paymentStrategy.getStrategy(payload.paymentGateway);
      const response = await gateway.initiatePayment(payload);

      return {
        success: true,
        data: response,
        message: 'Payment initiated successfully',
      };
    } catch (error) {
      this.logger.error('Error initiating payment', error);
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Verify a payment transaction
   */
  async verifyPayment(payload: VerifyPaymentDto) {
    try {
      const paymentGateway = payload.paymentGateway.toUpperCase() as PaymentGatewayEnum;
      const gateway = this.paymentStrategy.getStrategy(paymentGateway);

      let verificationResult: any;

      // Handle different payment gateway DTOs using strategy pattern
      switch (paymentGateway) {
        case PaymentGatewayEnum.KHALTI:
          verificationResult = await this.verifyKhaltiPayment(gateway, payload as KhaltiPaymentDto);
          break;
        case PaymentGatewayEnum.ESEWA:
          verificationResult = await this.verifyEsewaPayment(gateway, payload as EsewaPaymentDto);
          break;
        case PaymentGatewayEnum.STRIPE:
          verificationResult = await this.verifyStripePayment(gateway, payload as StripePaymentDto);
          break;
        default:
          throw new Error(`Unsupported payment gateway: ${paymentGateway}`);
      }

      return {
        success: true,
        data: verificationResult,
        message: 'Payment verification completed',
      };
    } catch (error) {
      this.logger.error('Error verifying payment', error);
      throw new BadRequestException(error.message);
    }
  }

  /**
   * Handle Stripe webhook events
   */
  async handleStripeWebhook(signature: string, request: Request) {
    try {
      if (!signature) {
        this.logger.warn('Missing Stripe signature');
        return {
          success: false,
          error: 'Missing stripe-signature header',
          message: 'Webhook signature verification failed',
        };
      }

      // Get raw body from request
      const payload = request.body;

      // Convert body to buffer if it's not already
      const rawBody = Buffer.isBuffer(payload) ? payload : Buffer.from(JSON.stringify(payload));

      // Get Stripe gateway
      const stripeGateway = this.paymentStrategy.getStrategy(PaymentGatewayEnum.STRIPE);

      // Process webhook - ensure handleWebhook exists
      if (typeof stripeGateway.handleWebhook === 'function') {
        const result = await stripeGateway.handleWebhook(signature, rawBody);
        return {
          success: true,
          data: result,
          message: 'Webhook processed successfully',
        };
      } else {
        throw new Error('Webhook handler not implemented for this gateway');
      }
    } catch (error) {
      this.logger.error('Error processing Stripe webhook', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to process webhook',
      };
    }
  }

  /**
   * Private method to verify Khalti payment
   */
  private async verifyKhaltiPayment(gateway: any, payload: KhaltiPaymentDto) {
    // Check if pidx is provided for Khalti payments
    if (!payload.pidx) {
      throw new BadRequestException('Must provide pidx for Khalti payment verification');
    }

    return await gateway.verifyPayment({
      pidx: payload.pidx,
    });
  }

  /**
   * Private method to verify Esewa payment
   */
  private async verifyEsewaPayment(gateway: any, payload: EsewaPaymentDto) {
    // Check if data is provided for esewa payments
    if (!payload.data) {
      throw new BadRequestException('Must provide data for esewa payment verification');
    }

    const decoded = Buffer.from(payload.data, 'base64').toString();
    const decodedJson = JSON.parse(decoded);

    // The data property contains base64 encoded verification data
    return await gateway.verifyPayment(decodedJson as IEsewaVerifyPayload);
  }

  /**
   * Private method to verify Stripe payment
   */
  private async verifyStripePayment(gateway: any, payload: StripePaymentDto) {
    // Check if session_id is provided for stripe payments
    if (!payload.session_id) {
      throw new BadRequestException('Must provide session_id for stripe payment verification');
    }

    return await gateway.verifyPayment({
      session_id: payload.session_id,
    });
  }
}
