import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { PrismaModule } from 'src/prisma/prisma.module';
import { PlansModule } from 'src/plans/plans.module';
import { KhaltiGatewayService } from './gateways/khalti-gateway.service';
import { PaymentStrategy } from './strategies/payment.strategy';
import { EsewaGatewayService } from './gateways/esewa-gateway.service';
import { StripeGatewayService } from './gateways/stripe-gateway.service';
import { AuditService } from './services/audit.service';

@Module({
  imports: [PrismaModule, PlansModule],
  controllers: [PaymentController],
  providers: [
    PaymentService,
    PaymentStrategy,
    KhaltiGatewayService,
    EsewaGatewayService,
    StripeGatewayService,
    AuditService,
    // {
    //   provide: 'STRIPE_API_KEY',
    //   useFactory: async (configService: ConfigService) => {
    //     return configService.get<string>('STRIPE_API_KEY');
    //   },
    //   inject: [ConfigService],
    // },
  ],
})
export class PaymentModule {}
