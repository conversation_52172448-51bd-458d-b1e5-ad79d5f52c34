import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'src/prisma/prisma.service';
import { IKhaltiVerifypayload, InitiatePaymentDto, KhaltiVerifySchema } from '../dto/payment.dto';
import axios from 'axios';
import { PlansService } from 'src/plans/plans.service';
import { parsePaisa } from 'src/_utils/currency.utils';
import { handleErrors } from 'src/_utils/handleErrors';

@Injectable()
export class KhaltiGatewayService {
  constructor(
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
    private readonly plansService: PlansService,
  ) {}
  //logger for the debugging
  private readonly logger = new Logger(KhaltiGatewayService.name);

  // Constant Variables for the enviroment file
  private readonly NODE_ENV = this.configService.getOrThrow<string>('NODE_ENV');
  private readonly KHALTI_PRIVATE_KEY = this.configService.getOrThrow<string>('KHALTI_PRIVATE_KEY');
  private readonly KHALTI_PUBLIC_KEY = this.configService.getOrThrow<string>('KHALTI_PUBLIC_KEY');
  private readonly KHALTI_TEST_URL = this.configService.getOrThrow<string>('KHALTI_TEST_URL');
  private readonly KHALTI_PROD_URL = this.configService.getOrThrow<string>('KHALTI_PROD_URL');
  private readonly CLIENT_BASE_URL = this.configService.getOrThrow<string>('CLIENT_BASE_URL');

  async initiatePayment(payload: InitiatePaymentDto) {
    try {
      // instanciate the axiso instance with the config
      const axios = this.axiosInstance();

      this.logger.log('Initiating Khalti payment', { payload });
      // extracting the necessary value from  the payload
      const { isPrimary, planId, tenantId = 'test', currency } = payload;

      // get the selected plan information
      const plan = await this.plansService.findOne(planId, { currency: currency });

      if (!plan || !plan.isActive) throw new NotFoundException('The selected plan is not avaiable right now!');

      const khaltiPayload = {
        amount: parsePaisa(Number(plan.amount)),
        purchase_order_id: `order-${tenantId}-${Date.now()}`,
        purchase_order_name: `Subscription- ${plan.name}`,
        return_url: `${this.CLIENT_BASE_URL}/payment/callback/`,
        website_url: `${this.CLIENT_BASE_URL}/`,
      };

      // debugger
      this.logger.debug('khalti-payload', khaltiPayload);

      const response = await axios.post('/epayment/initiate/', khaltiPayload);

      this.logger.log('Khalti payment initiated successfully', {
        purchase_order_id: khaltiPayload.purchase_order_id,
        amount: khaltiPayload.amount,
      });

      return response.data;
    } catch (error) {
      this.logger.error('Error initiating Khalti payment', {
        error: error.message,
        payload,
        stack: error.stack,
      });
      handleErrors(error);
    }
  }

  async verifyPayment(payload: IKhaltiVerifypayload) {
    try {
      const result = KhaltiVerifySchema.safeParse(payload);
      if (!result.success) {
        throw new BadRequestException(result.error.issues[0]?.message || 'Validation failed');
      }

      this.logger.log('Verifying Khalti payment', { pidx: payload.pidx });

      const axios = this.axiosInstance();
      const response = await axios.post('/epayment/lookup/', {
        pidx: payload.pidx,
      });

      this.logger.debug('khalti-verify-response', response.data);

      // handle the subscription after this when the payment is successfull
      switch (response.data.status) {
        case 'Completed':
          this.logger.log('Khalti payment verified successfully', {
            pidx: payload.pidx,
            status: response.data.status,
          });

          //create subscription here
          return response.data;

        case 'Refunded':
          this.logger.log('Khalti payment refunded', {
            pidx: payload.pidx,
            status: response.data.status,
          });
          return response.data;

        default:
          throw new BadRequestException(`Payment verification failed: ${response.data.status}`, {
            cause: response.data,
          });
      }
    } catch (error) {
      handleErrors(error);
    }
  }

  private axiosInstance() {
    return axios.create({
      baseURL: this.NODE_ENV === 'development' ? this.KHALTI_TEST_URL : this.KHALTI_PROD_URL,
      headers: {
        Authorization: `Key ${this.KHALTI_PRIVATE_KEY}`,
        'Content-Type': 'application/json',
      },
    });
  }
}
