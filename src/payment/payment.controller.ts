import { Request } from 'express';
import { Body, Controller, Headers, Post, Req, HttpStatus, HttpCode, Get, Query } from '@nestjs/common';
import { InitiatePaymentDto, VerifyPaymentDto } from './dto/payment.dto';
import { PaymentService } from './payment.service';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PublicEndpoint } from 'src/auth/decorators/publicEnpoint.decorator';
import { PaymentHistoryQueryDto } from './dto/enhanced-payment.dto';
import { AuditContext } from './dto/audit-log.dto';

@Controller('payment')
@PublicEndpoint()
// @Roles('TENANT_ADMIN')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  /**
   * Initiate a payment transaction
   */
  @Post('initiate-payment')
  @ApiOperation({ summary: 'Initiate a payment transaction' })
  @ApiResponse({
    status: 200,
    description: 'Payment initiated successfully. Returns redirect URL or session data.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid payment gateway or missing required fields.',
  })
  async initiatePayment(@Body() payload: InitiatePaymentDto, @Req() request: Request) {
    const auditContext: AuditContext = {
      tenantId: payload.tenantId,
      ipAddress: request.ip || request.socket?.remoteAddress,
      userAgent: request.get('User-Agent'),
      metadata: {
        endpoint: 'initiate-payment',
        method: 'POST',
      },
    };

    return this.paymentService.initiatePayment(payload, auditContext);
  }

  /**
   * Verify a payment transaction
   */
  @Post('verify-payment')
  @ApiOperation({ summary: 'Verify a payment transaction' })
  @ApiResponse({
    status: 200,
    description: 'Payment verification result',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid transaction ID or payment gateway.',
  })
  async verifyPayment(@Body() payload: VerifyPaymentDto, @Req() request: Request) {
    const auditContext: AuditContext = {
      ipAddress: request.ip || request.socket?.remoteAddress,
      userAgent: request.get('User-Agent'),
      metadata: {
        endpoint: 'verify-payment',
        method: 'POST',
        paymentGateway: payload.paymentGateway,
      },
    };

    return this.paymentService.verifyPayment(payload, auditContext);
  }

  /**
   * Handle Stripe webhook events
   */
  @Post('webhook/stripe')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle Stripe webhook events' })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid signature or payload.',
  })
  async handleStripeWebhook(@Headers('stripe-signature') signature: string, @Req() request: Request) {
    return this.paymentService.handleStripeWebhook(signature, request);
  }

  /**
   * Get payment history for a tenant
   */
  @Get('history')
  @ApiOperation({ summary: 'Get payment history' })
  @ApiResponse({
    status: 200,
    description: 'Payment history retrieved successfully',
  })
  async getPaymentHistory(@Query() query: PaymentHistoryQueryDto) {
    return this.paymentService.getPaymentHistory(query);
  }
}
