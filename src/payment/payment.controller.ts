import { Request } from 'express';
import { Body, Controller, Headers, Post, Req, HttpStatus, HttpCode } from '@nestjs/common';
import { InitiatePaymentDto, VerifyPaymentDto } from './dto/payment.dto';
import { PaymentService } from './payment.service';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PublicEndpoint } from 'src/auth/decorators/publicEnpoint.decorator';

@Controller('payment')
@PublicEndpoint()
// @Roles('TENANT_ADMIN')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  /**
   * Initiate a payment transaction
   */
  @Post('initiate-payment')
  @ApiOperation({ summary: 'Initiate a payment transaction' })
  @ApiResponse({
    status: 200,
    description: 'Payment initiated successfully. Returns redirect URL or session data.',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid payment gateway or missing required fields.',
  })
  async initiatePayment(@Body() payload: InitiatePaymentDto) {
    return this.paymentService.initiatePayment(payload);
  }

  /**
   * Verify a payment transaction
   */
  @Post('verify-payment')
  @ApiOperation({ summary: 'Verify a payment transaction' })
  @ApiResponse({
    status: 200,
    description: 'Payment verification result',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid transaction ID or payment gateway.',
  })
  async verifyPayment(@Body() payload: VerifyPaymentDto) {
    return this.paymentService.verifyPayment(payload);
  }

  /**
   * Handle Stripe webhook events
   */
  @Post('webhook/stripe')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle Stripe webhook events' })
  @ApiResponse({
    status: 200,
    description: 'Webhook processed successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request. Invalid signature or payload.',
  })
  async handleStripeWebhook(@Headers('stripe-signature') signature: string, @Req() request: Request) {
    return this.paymentService.handleStripeWebhook(signature, request);
  }
}
