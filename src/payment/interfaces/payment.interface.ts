import {
  IEsewaInitiateResponse,
  IEsewaVerifyPayload,
  IKhaltiInitiateResponse,
  IKhaltiVerifypayload,
  IKhaltiVerifyResponse,
  InitiatePaymentDto,
  IPaymentResponse,
  IStripeInitiateResponse,
  IStripeVerifyResponse,
  VerifyPaymentDto,
  WebhookPayloadDto,
} from '../dto/payment.dto';

/**
 * Common interface for all payment gateways
 * Each gateway must implement these methods
 */
export interface PaymentGateway {
  initiatePayment(
    payload: InitiatePaymentDto,
  ): Promise<IEsewaInitiateResponse | IKhaltiInitiateResponse | IStripeInitiateResponse | IPaymentResponse>;

  /**
   * Verify a payment transaction
   * @param payload Payment verification data
   * @returns Verification result
   */
  verifyPayment(
    payload: IKhaltiVerifypayload | IEsewaVerifyPayload | VerifyPaymentDto,
  ): Promise<IKhaltiVerifyResponse | IStripeVerifyResponse | IPaymentResponse>;

  /**
   * Handle webhook notifications from payment provider
   * @param signature Webhook signature for verification
   * @param payload Raw webhook payload
   * @returns Processing result
   */
  handleWebhook?(signature: string, payload: Buffer): Promise<IPaymentResponse>;
}

/**
 * Interface for subscription-capable payment gateways
 * Extends the base PaymentGateway interface
 */
export interface SubscriptionPaymentGateway extends PaymentGateway {
  // cancel subscription
  cancelSubscription?(subscriptionId: string): Promise<IPaymentResponse>;

  // update subscription
  updateSubscription?<T>(subscriptionId: string, updateData: T): Promise<IPaymentResponse>;

  // get subscription
  getSubscription?(subscriptionId: string): Promise<IPaymentResponse>;
}
