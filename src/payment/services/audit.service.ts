import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import {
  AuditAction,
  AuditEntityType,
  AuditStatus,
  AuditContext,
  CreateAuditLogDto,
  QueryAuditLogsDto,
} from '../dto/audit-log.dto';

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(private readonly prismaService: PrismaService) {}

  /**
   * Create an audit log entry
   */
  async createAuditLog(
    action: AuditAction,
    entityType: AuditEntityType,
    context: AuditContext,
    options?: {
      entityId?: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      status?: AuditStatus;
      errorMessage?: string;
      metadata?: Record<string, any>;
    }
  ) {
    try {
      const auditLog = await this.prismaService.auditLog.create({
        data: {
          action,
          entityType,
          entityId: options?.entityId,
          userId: context.userId,
          tenantId: context.tenantId,
          ipAddress: context.ipAddress,
          userAgent: context.userAgent,
          metadata: {
            ...context.metadata,
            ...options?.metadata,
          },
          oldValues: options?.oldValues,
          newValues: options?.newValues,
          status: options?.status || AuditStatus.SUCCESS,
          errorMessage: options?.errorMessage,
        },
      });

      this.logger.log(`Audit log created: ${action} for ${entityType}`, {
        auditLogId: auditLog.id,
        entityId: options?.entityId,
        userId: context.userId,
        tenantId: context.tenantId,
      });

      return auditLog;
    } catch (error) {
      this.logger.error('Failed to create audit log', {
        action,
        entityType,
        error: error.message,
        context,
      });
      throw error;
    }
  }

  /**
   * Log payment initiation
   */
  async logPaymentInitiation(
    context: AuditContext,
    paymentData: {
      transactionId: string;
      paymentGateway: string;
      amount: number;
      currency: string;
      planId: string;
      tenantId: string;
    }
  ) {
    return this.createAuditLog(
      AuditAction.PAYMENT_INITIATED,
      AuditEntityType.PAYMENT,
      context,
      {
        entityId: paymentData.transactionId,
        newValues: {
          paymentGateway: paymentData.paymentGateway,
          amount: paymentData.amount,
          currency: paymentData.currency,
          planId: paymentData.planId,
          tenantId: paymentData.tenantId,
        },
        metadata: {
          paymentGateway: paymentData.paymentGateway,
          amount: paymentData.amount,
          currency: paymentData.currency,
          planId: paymentData.planId,
        },
      }
    );
  }

  /**
   * Log payment verification
   */
  async logPaymentVerification(
    context: AuditContext,
    verificationData: {
      transactionId: string;
      paymentGateway: string;
      status: string;
      previousStatus?: string;
      amount: number;
      currency: string;
    },
    success: boolean = true,
    errorMessage?: string
  ) {
    return this.createAuditLog(
      AuditAction.PAYMENT_VERIFIED,
      AuditEntityType.PAYMENT,
      context,
      {
        entityId: verificationData.transactionId,
        oldValues: verificationData.previousStatus ? {
          status: verificationData.previousStatus,
        } : undefined,
        newValues: {
          status: verificationData.status,
          verifiedAt: new Date().toISOString(),
        },
        status: success ? AuditStatus.SUCCESS : AuditStatus.FAILED,
        errorMessage,
        metadata: {
          paymentGateway: verificationData.paymentGateway,
          amount: verificationData.amount,
          currency: verificationData.currency,
          finalStatus: verificationData.status,
        },
      }
    );
  }

  /**
   * Log subscription creation/update
   */
  async logSubscriptionAction(
    action: AuditAction.SUBSCRIPTION_CREATED | AuditAction.SUBSCRIPTION_UPDATED | AuditAction.SUBSCRIPTION_CANCELLED,
    context: AuditContext,
    subscriptionData: {
      subscriptionId: string;
      planId: string;
      status: string;
      previousStatus?: string;
      startDate: Date;
      endDate: Date;
    }
  ) {
    return this.createAuditLog(
      action,
      AuditEntityType.SUBSCRIPTION,
      context,
      {
        entityId: subscriptionData.subscriptionId,
        oldValues: subscriptionData.previousStatus ? {
          status: subscriptionData.previousStatus,
        } : undefined,
        newValues: {
          planId: subscriptionData.planId,
          status: subscriptionData.status,
          startDate: subscriptionData.startDate.toISOString(),
          endDate: subscriptionData.endDate.toISOString(),
        },
        metadata: {
          planId: subscriptionData.planId,
          status: subscriptionData.status,
          durationDays: Math.ceil((subscriptionData.endDate.getTime() - subscriptionData.startDate.getTime()) / (1000 * 60 * 60 * 24)),
        },
      }
    );
  }

  /**
   * Log transaction creation/update
   */
  async logTransactionAction(
    action: AuditAction.TRANSACTION_CREATED | AuditAction.TRANSACTION_UPDATED,
    context: AuditContext,
    transactionData: {
      transactionId: string;
      amount: number;
      currency: string;
      status: string;
      previousStatus?: string;
      transactionType: string;
      paymentGateway: string;
    }
  ) {
    return this.createAuditLog(
      action,
      AuditEntityType.TRANSACTION,
      context,
      {
        entityId: transactionData.transactionId,
        oldValues: transactionData.previousStatus ? {
          status: transactionData.previousStatus,
        } : undefined,
        newValues: {
          amount: transactionData.amount,
          currency: transactionData.currency,
          status: transactionData.status,
          transactionType: transactionData.transactionType,
        },
        metadata: {
          amount: transactionData.amount,
          currency: transactionData.currency,
          transactionType: transactionData.transactionType,
          paymentGateway: transactionData.paymentGateway,
        },
      }
    );
  }

  /**
   * Log webhook processing
   */
  async logWebhookProcessing(
    context: AuditContext,
    webhookData: {
      eventType: string;
      paymentGateway: string;
      processed: boolean;
      errorMessage?: string;
    }
  ) {
    return this.createAuditLog(
      webhookData.processed ? AuditAction.WEBHOOK_PROCESSED : AuditAction.WEBHOOK_FAILED,
      AuditEntityType.WEBHOOK,
      context,
      {
        status: webhookData.processed ? AuditStatus.SUCCESS : AuditStatus.FAILED,
        errorMessage: webhookData.errorMessage,
        metadata: {
          eventType: webhookData.eventType,
          paymentGateway: webhookData.paymentGateway,
          processed: webhookData.processed,
        },
      }
    );
  }

  /**
   * Query audit logs with filters
   */
  async queryAuditLogs(query: QueryAuditLogsDto) {
    const {
      action,
      entityType,
      entityId,
      userId,
      tenantId,
      status,
      startDate,
      endDate,
      page,
      limit,
    } = query;

    const where: any = {};

    if (action) where.action = action;
    if (entityType) where.entityType = entityType;
    if (entityId) where.entityId = entityId;
    if (userId) where.userId = userId;
    if (tenantId) where.tenantId = tenantId;
    if (status) where.status = status;

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt.gte = new Date(startDate);
      if (endDate) where.createdAt.lte = new Date(endDate);
    }

    const [auditLogs, total] = await Promise.all([
      this.prismaService.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          tenant: {
            select: {
              id: true,
              organizationName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      this.prismaService.auditLog.count({ where }),
    ]);

    return {
      auditLogs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1,
      },
    };
  }
}
