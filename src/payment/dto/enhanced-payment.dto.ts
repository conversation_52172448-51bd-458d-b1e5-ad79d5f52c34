import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';
import { Currency, PaymentGateway, TransactionStatus, TransactionType, SubscriptionStatus } from '@prisma/client';

// Enhanced payment initiation response
export const EnhancedPaymentInitiationResponseSchema = z.object({
  success: z.boolean().describe('Whether the payment initiation was successful'),
  data: z.object({
    // Gateway response data
    gatewayResponse: z.record(z.any()).describe('Raw response from payment gateway'),
    // Database records
    transaction: z.object({
      id: z.string().uuid(),
      amount: z.number(),
      currency: z.nativeEnum(Currency),
      status: z.nativeEnum(TransactionStatus),
      transactionType: z.nativeEnum(TransactionType),
      purchaseOrderId: z.string().nullable(),
      purchaseOrderName: z.string().nullable(),
      createdAt: z.date(),
    }).describe('Created transaction record'),
    paymentMethod: z.object({
      id: z.string().uuid(),
      paymentGateway: z.nativeEnum(PaymentGateway),
      isPrimary: z.boolean(),
      tenantId: z.string().uuid(),
      createdAt: z.date(),
    }).describe('Payment method record'),
    // Redirect information
    redirectUrl: z.string().url().optional().describe('URL to redirect user for payment'),
    sessionId: z.string().optional().describe('Payment session ID'),
  }),
  message: z.string().describe('Response message'),
  auditLogId: z.string().uuid().describe('ID of the audit log entry'),
});

export class EnhancedPaymentInitiationResponseDto extends createZodDto(EnhancedPaymentInitiationResponseSchema) {}

// Enhanced payment verification response
export const EnhancedPaymentVerificationResponseSchema = z.object({
  success: z.boolean().describe('Whether the payment verification was successful'),
  data: z.object({
    // Gateway verification data
    gatewayResponse: z.record(z.any()).describe('Raw response from payment gateway'),
    // Updated database records
    transaction: z.object({
      id: z.string().uuid(),
      amount: z.number(),
      currency: z.nativeEnum(Currency),
      status: z.nativeEnum(TransactionStatus),
      transactionType: z.nativeEnum(TransactionType),
      transactionDetails: z.record(z.any()),
      updatedAt: z.date(),
    }).describe('Updated transaction record'),
    subscription: z.object({
      id: z.string().uuid(),
      tenantId: z.string().uuid(),
      planId: z.string().uuid(),
      status: z.nativeEnum(SubscriptionStatus),
      startDate: z.date(),
      endDate: z.date(),
      createdAt: z.date(),
      updatedAt: z.date(),
    }).optional().describe('Created or updated subscription record'),
    // Payment status
    paymentStatus: z.enum(['completed', 'failed', 'pending', 'cancelled']).describe('Final payment status'),
    isSubscriptionActive: z.boolean().describe('Whether subscription is now active'),
  }),
  message: z.string().describe('Response message'),
  auditLogId: z.string().uuid().describe('ID of the audit log entry'),
});

export class EnhancedPaymentVerificationResponseDto extends createZodDto(EnhancedPaymentVerificationResponseSchema) {}

// Payment history query schema
export const PaymentHistoryQuerySchema = z.object({
  tenantId: z.string().uuid().optional().describe('Filter by tenant ID'),
  paymentGateway: z.nativeEnum(PaymentGateway).optional().describe('Filter by payment gateway'),
  status: z.nativeEnum(TransactionStatus).optional().describe('Filter by transaction status'),
  transactionType: z.nativeEnum(TransactionType).optional().describe('Filter by transaction type'),
  startDate: z.string().datetime().optional().describe('Filter from this date'),
  endDate: z.string().datetime().optional().describe('Filter to this date'),
  page: z.number().int().min(1).default(1).describe('Page number'),
  limit: z.number().int().min(1).max(100).default(20).describe('Number of items per page'),
  sortBy: z.enum(['createdAt', 'amount', 'status']).default('createdAt').describe('Sort field'),
  sortOrder: z.enum(['asc', 'desc']).default('desc').describe('Sort order'),
});

export class PaymentHistoryQueryDto extends createZodDto(PaymentHistoryQuerySchema) {}

// Payment history response
export const PaymentHistoryResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    transactions: z.array(z.object({
      id: z.string().uuid(),
      amount: z.number(),
      currency: z.nativeEnum(Currency),
      status: z.nativeEnum(TransactionStatus),
      transactionType: z.nativeEnum(TransactionType),
      purchaseOrderId: z.string().nullable(),
      purchaseOrderName: z.string().nullable(),
      transactionDetails: z.record(z.any()),
      createdAt: z.date(),
      updatedAt: z.date(),
      paymentMethod: z.object({
        id: z.string().uuid(),
        paymentGateway: z.nativeEnum(PaymentGateway),
        isPrimary: z.boolean(),
      }).nullable(),
      plan: z.object({
        id: z.string().uuid(),
        name: z.string(),
        description: z.string().nullable(),
        amount: z.number(),
      }),
      subscription: z.object({
        id: z.string().uuid(),
        status: z.nativeEnum(SubscriptionStatus),
        startDate: z.date(),
        endDate: z.date(),
      }).nullable(),
    })),
    pagination: z.object({
      page: z.number(),
      limit: z.number(),
      total: z.number(),
      totalPages: z.number(),
      hasNext: z.boolean(),
      hasPrev: z.boolean(),
    }),
  }),
  message: z.string(),
});

export class PaymentHistoryResponseDto extends createZodDto(PaymentHistoryResponseSchema) {}

// Subscription tracking response
export const SubscriptionTrackingResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    subscription: z.object({
      id: z.string().uuid(),
      tenantId: z.string().uuid(),
      planId: z.string().uuid(),
      status: z.nativeEnum(SubscriptionStatus),
      startDate: z.date(),
      endDate: z.date(),
      createdAt: z.date(),
      updatedAt: z.date(),
      plan: z.object({
        id: z.string().uuid(),
        name: z.string(),
        description: z.string().nullable(),
        amount: z.number(),
        interval: z.string(),
        durationInDays: z.number(),
        features: z.record(z.any()).nullable(),
      }),
      tenant: z.object({
        id: z.string().uuid(),
        organizationName: z.string(),
        organizationWebsite: z.string().nullable(),
      }),
    }).nullable(),
    paymentMethods: z.array(z.object({
      id: z.string().uuid(),
      paymentGateway: z.nativeEnum(PaymentGateway),
      isPrimary: z.boolean(),
      createdAt: z.date(),
    })),
    recentTransactions: z.array(z.object({
      id: z.string().uuid(),
      amount: z.number(),
      currency: z.nativeEnum(Currency),
      status: z.nativeEnum(TransactionStatus),
      transactionType: z.nativeEnum(TransactionType),
      createdAt: z.date(),
    })),
    subscriptionMetrics: z.object({
      daysRemaining: z.number(),
      isExpiringSoon: z.boolean(),
      totalTransactions: z.number(),
      totalAmountPaid: z.number(),
      lastPaymentDate: z.date().nullable(),
      nextBillingDate: z.date().nullable(),
    }),
  }),
  message: z.string(),
});

export class SubscriptionTrackingResponseDto extends createZodDto(SubscriptionTrackingResponseSchema) {}

// Webhook processing response
export const WebhookProcessingResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    webhookId: z.string().uuid().describe('Unique identifier for this webhook processing'),
    eventType: z.string().describe('Type of webhook event'),
    processed: z.boolean().describe('Whether the webhook was successfully processed'),
    transactionUpdated: z.boolean().describe('Whether any transaction was updated'),
    subscriptionUpdated: z.boolean().describe('Whether any subscription was updated'),
    auditLogId: z.string().uuid().describe('ID of the audit log entry'),
    processingTime: z.number().describe('Time taken to process webhook in milliseconds'),
  }),
  message: z.string(),
  error: z.string().optional(),
});

export class WebhookProcessingResponseDto extends createZodDto(WebhookProcessingResponseSchema) {}
