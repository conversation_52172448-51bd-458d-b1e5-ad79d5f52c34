import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

// Enums for audit logging (these should match the Prisma schema)
export enum AuditAction {
  // Payment actions
  PAYMENT_INITIATED = 'PAYMENT_INITIATED',
  PAYMENT_VERIFIED = 'PAYMENT_VERIFIED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  PAYMENT_CANCELLED = 'PAYMENT_CANCELLED',
  PAYMENT_REFUNDED = 'PAYMENT_REFUNDED',
  
  // Subscription actions
  SUBSCRIPTION_CREATED = 'SUBSCRIPTION_CREATED',
  SUBSCRIPTION_UPDATED = 'SUBSCRIPTION_UPDATED',
  SUBSCRIPTION_CANCELLED = 'SUBSCRIPTION_CANCELLED',
  SUBSCRIPTION_RENEWED = 'SUBSCRIPTION_RENEWED',
  SUBSCRIPTION_EXPIRED = 'SUBSCRIPTION_EXPIRED',
  
  // Payment method actions
  PAYMENT_METHOD_ADDED = 'PAYMENT_METHOD_ADDED',
  PAYMENT_METHOD_UPDATED = 'PAYMENT_METHOD_UPDATED',
  PAYMENT_METHOD_REMOVED = 'PAYMENT_METHOD_REMOVED',
  PAYMENT_METHOD_SET_PRIMARY = 'PAYMENT_METHOD_SET_PRIMARY',
  
  // Transaction actions
  TRANSACTION_CREATED = 'TRANSACTION_CREATED',
  TRANSACTION_UPDATED = 'TRANSACTION_UPDATED',
  TRANSACTION_REVERSED = 'TRANSACTION_REVERSED',
  
  // Webhook actions
  WEBHOOK_RECEIVED = 'WEBHOOK_RECEIVED',
  WEBHOOK_PROCESSED = 'WEBHOOK_PROCESSED',
  WEBHOOK_FAILED = 'WEBHOOK_FAILED',
}

export enum AuditEntityType {
  PAYMENT = 'PAYMENT',
  TRANSACTION = 'TRANSACTION',
  SUBSCRIPTION = 'SUBSCRIPTION',
  PAYMENT_METHOD = 'PAYMENT_METHOD',
  PLAN = 'PLAN',
  USER = 'USER',
  TENANT = 'TENANT',
  WEBHOOK = 'WEBHOOK',
}

export enum AuditStatus {
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  PENDING = 'PENDING',
}

// Base audit log schema
export const AuditLogSchema = z.object({
  action: z.nativeEnum(AuditAction).describe('The action that was performed'),
  entityType: z.nativeEnum(AuditEntityType).describe('The type of entity affected'),
  entityId: z.string().uuid().optional().describe('The ID of the entity affected'),
  userId: z.string().uuid().optional().describe('The ID of the user who performed the action'),
  tenantId: z.string().uuid().optional().describe('The ID of the tenant'),
  ipAddress: z.string().optional().describe('IP address of the user'),
  userAgent: z.string().optional().describe('User agent string'),
  metadata: z.record(z.any()).optional().describe('Additional context data'),
  oldValues: z.record(z.any()).optional().describe('Previous state for updates'),
  newValues: z.record(z.any()).optional().describe('New state for updates'),
  status: z.nativeEnum(AuditStatus).default(AuditStatus.SUCCESS).describe('Status of the action'),
  errorMessage: z.string().optional().describe('Error message if action failed'),
});

// Create audit log DTO
export class CreateAuditLogDto extends createZodDto(AuditLogSchema) {}

// Audit log response schema
export const AuditLogResponseSchema = AuditLogSchema.extend({
  id: z.string().uuid().describe('Unique identifier for the audit log'),
  createdAt: z.date().describe('When the audit log was created'),
});

export class AuditLogResponseDto extends createZodDto(AuditLogResponseSchema) {}

// Query audit logs schema
export const QueryAuditLogsSchema = z.object({
  action: z.nativeEnum(AuditAction).optional().describe('Filter by action'),
  entityType: z.nativeEnum(AuditEntityType).optional().describe('Filter by entity type'),
  entityId: z.string().uuid().optional().describe('Filter by entity ID'),
  userId: z.string().uuid().optional().describe('Filter by user ID'),
  tenantId: z.string().uuid().optional().describe('Filter by tenant ID'),
  status: z.nativeEnum(AuditStatus).optional().describe('Filter by status'),
  startDate: z.string().datetime().optional().describe('Filter from this date'),
  endDate: z.string().datetime().optional().describe('Filter to this date'),
  page: z.number().int().min(1).default(1).describe('Page number'),
  limit: z.number().int().min(1).max(100).default(20).describe('Number of items per page'),
});

export class QueryAuditLogsDto extends createZodDto(QueryAuditLogsSchema) {}

// Audit log context interface for internal use
export interface AuditContext {
  userId?: string;
  tenantId?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, any>;
}

// Payment-specific audit metadata interfaces
export interface PaymentAuditMetadata {
  paymentGateway: string;
  amount: number;
  currency: string;
  planId: string;
  transactionId?: string;
  sessionId?: string;
  purchaseOrderId?: string;
}

export interface SubscriptionAuditMetadata {
  planId: string;
  previousPlanId?: string;
  status: string;
  previousStatus?: string;
  startDate: string;
  endDate: string;
}

export interface TransactionAuditMetadata {
  amount: number;
  currency: string;
  status: string;
  previousStatus?: string;
  paymentGateway: string;
  transactionType: string;
}
