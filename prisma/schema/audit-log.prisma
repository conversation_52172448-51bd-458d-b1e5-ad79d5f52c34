// Audit log schema for tracking system events and user actions
model AuditLog {
  id          String          @id @default(uuid()) @db.Uuid
  action      AuditAction     @map("action")
  entityType  AuditEntityType @map("entity_type")
  entityId    String?         @map("entity_id") @db.Uuid
  userId      String?         @map("user_id") @db.Uuid
  tenantId    String?         @map("tenant_id") @db.Uuid
  ipAddress   String?         @map("ip_address")
  userAgent   String?         @map("user_agent")
  metadata    Json?           @map("metadata") // Additional context data
  oldValues   Json?           @map("old_values") // Previous state for updates
  newValues   Json?           @map("new_values") // New state for updates
  status      AuditStatus     @default(SUCCESS) @map("status")
  errorMessage String?        @map("error_message")
  createdAt   DateTime        @default(now()) @map("created_at")

  // Relations
  user   User?   @relation(fields: [userId], references: [id])
  tenant Tenant? @relation(fields: [tenantId], references: [id])

  @@index([action])
  @@index([entityType])
  @@index([entityId])
  @@index([userId])
  @@index([tenantId])
  @@index([createdAt])
  @@index([status])
  @@map("audit_logs")
}

enum AuditAction {
  // Payment actions
  PAYMENT_INITIATED
  PAYMENT_VERIFIED
  PAYMENT_FAILED
  PAYMENT_CANCELLED
  PAYMENT_REFUNDED
  
  // Subscription actions
  SUBSCRIPTION_CREATED
  SUBSCRIPTION_UPDATED
  SUBSCRIPTION_CANCELLED
  SUBSCRIPTION_RENEWED
  SUBSCRIPTION_EXPIRED
  
  // Payment method actions
  PAYMENT_METHOD_ADDED
  PAYMENT_METHOD_UPDATED
  PAYMENT_METHOD_REMOVED
  PAYMENT_METHOD_SET_PRIMARY
  
  // Transaction actions
  TRANSACTION_CREATED
  TRANSACTION_UPDATED
  TRANSACTION_REVERSED
  
  // Webhook actions
  WEBHOOK_RECEIVED
  WEBHOOK_PROCESSED
  WEBHOOK_FAILED
  
  // User actions
  USER_LOGIN
  USER_LOGOUT
  USER_CREATED
  USER_UPDATED
  USER_DELETED
  
  // Tenant actions
  TENANT_CREATED
  TENANT_UPDATED
  TENANT_DELETED
}

enum AuditEntityType {
  PAYMENT
  TRANSACTION
  SUBSCRIPTION
  PAYMENT_METHOD
  PLAN
  USER
  TENANT
  WEBHOOK
}

enum AuditStatus {
  SUCCESS
  FAILED
  PENDING
}
