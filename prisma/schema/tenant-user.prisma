// Tenant and user management schema
model Tenant {
  id                  String             @id @default(uuid()) @db.Uuid
  organizationName    String             @unique @map("organization_name")
  organizationWebsite String?            @unique @map("organization_website")
  roleAtOrganization  String             @map("role_at_organization")
  contactNumber       String             @map("contact_number")
  createdAt           DateTime           @default(now()) @map("created_at")
  updatedAt           DateTime           @updatedAt @map("updated_at")
  // Relations
  subscription        Subscription?
  tenantUser          TenantUser[]
  TenantInvitation    TenantInvitation[]
  paymentMethod       PaymentMethod[]
  auditLogs           AuditLog[]

  @@index([organizationName])
  @@index([organizationWebsite])
  @@map("tenants")
}

model User {
  id                    String    @id @default(uuid()) @db.Uuid
  email                 String    @unique
  name                  String
  password              Password? @relation("UserPassword")
  createdAt             DateTime  @default(now()) @map("created_at")
  updatedAt             DateTime  @updatedAt @map("updated_at")
  isVerified            <PERSON>olean   @default(false) @map("is_verified")
  verificationToken     String?   @map("verification_token") @db.Uuid
  verificationExpiresAt DateTime? @map("verification_expires_at")

  userStatus UserStatus @default(OFFLINE) @map("user_status")

  // Relationships
  refreshToken RefreshToken?
  tenantUser   TenantUser? // Changed to singular
  Role         Role          @relation(fields: [roleId], references: [id])
  roleId       String        @db.Uuid
  auditLogs    AuditLog[]

  @@index([email])
  @@map("users")
}

model TenantUser {
  id        String   @id @default(uuid()) @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Required relationships
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  role   Role   @relation(fields: [roleId], references: [id], onDelete: Restrict)

  // Required foreign keys
  userId   String @unique @map("user_id") @db.Uuid
  tenantId String @map("tenant_id") @db.Uuid
  roleId   String @map("role_id") @db.Uuid

  @@index([tenantId])
  @@index([userId])
  @@index([roleId])
  @@map("tenant_users")
}

model Password {
  id                     String    @id @default(uuid()) @db.Uuid
  hash                   String
  salt                   String
  passwordResetToken     String?   @map("password_reset_token")
  passwordResetExpiresAt DateTime? @map("password_reset_expires_at")
  //relations
  user                   User      @relation("UserPassword", fields: [userId], references: [id], onDelete: Cascade)
  userId                 String    @unique @map("user_id") @db.Uuid

  @@map("passwords")
}

model RefreshToken {
  id        String   @id @default(uuid()) @db.Uuid
  token     String   @unique @db.Uuid
  expiresAt DateTime @map("expires_at")
  isRevoked Boolean  @default(false) @map("is_revoked")
  userId    String   @unique @map("user_id") @db.Uuid
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  //relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  @@index([userId])
  @@map("refresh_tokens")
}

// For invitation management
model TenantInvitation {
  id         String    @id @default(uuid()) @db.Uuid
  email      String
  token      String    @unique @map("invitation_token")
  roleId     String    @map("role_id") @db.Uuid
  isActive   Boolean   @default(true) @map("is_active")
  acceptedAt DateTime? @map("accepted_at")
  expiresAt  DateTime  @map("expires_at")
  createdAt  DateTime  @default(now()) @map("created_at")
  tenant     Tenant    @relation(fields: [tenantId], references: [id])
  tenantId   String    @map("tenant_id") @db.Uuid

  @@index([email])
  @@index([token])
  @@map("tenant_invitations")
}

//Role model for RBAC
model Role {
  id          String           @id @default(uuid()) @db.Uuid
  type        SystemRoles      @unique
  createdAt   DateTime         @default(now()) @map("created_at")
  updatedAt   DateTime         @updatedAt @map("updated_at")
  //relations
  users       User[]
  permissions RolePermission[]
  TenantUser  TenantUser[]

  @@map("roles")
}

model Permission {
  id        String           @id @default(uuid()) @db.Uuid
  name      PermissionKey    @unique
  createdAt DateTime         @default(now()) @map("created_at")
  updatedAt DateTime         @updatedAt @map("updated_at")
  //relations
  roles     RolePermission[]

  @@map("permissions")
}

model RolePermission {
  roleId       String   @map("role_id") @db.Uuid
  permissionId String   @map("permission_id") @db.Uuid
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  //relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@id([roleId, permissionId])
  @@index([roleId, permissionId])
  @@map("role_permissions")
}

enum SystemRoles {
  SUPER_ADMIN
  TENANT_ADMIN
  MEMBER
  GUEST
}

enum UserStatus {
  ONLINE
  OFFLINE
  BUSY
  AWAY
}

enum PermissionKey {
  MANAGE_TENANT
  MANAGE_USERS
  MANAGE_ROLES
  VIEW_CONTENT
  EDIT_OWN_PROFILE
  MANAGE_BILLING
  ASSIGN_MESSAGES
  VIEW_ASSIGNED_MESSAGES
  DELETE_MESSAGES
}
